{{--
    Reusable Confirmation Modal Component
    
    This component provides a standardized confirmation modal for destructive actions
    across the admin interface with consistent styling and behavior.
    
    Parameters:
    - $modalId: Unique ID for the modal (default: 'confirmationModal')
    - $icon: Remix icon class for the header warning icon (default: 'ri-error-warning-line')
    - $iconSize: Font size for the icon (default: '6rem')
    - $title: Main confirmation text (required)
    - $warningText: Warning message text (default: 'This action cannot be undone!')
    - $cancelText: Cancel button text (default: 'No, Cancel')
    - $confirmText: Confirm button text (default: 'Yes, Confirm')
    - $formAction: Form action URL for the confirmation (required)
    - $formMethod: HTTP method for the form (default: 'DELETE')
    - $targetElementId: ID of the element to update with dynamic content (optional)
    
    Usage Examples:
    
    Basic Usage:
    <x-confirmation-modal 
        modal-id="deleteUserModal"
        title="Are you sure you want to delete this user?"
        form-action="{{ route('admin.users.destroy', $user) }}"
        confirm-text="Yes, Delete User"
    />
    
    Custom Icon and Styling:
    <x-confirmation-modal 
        modal-id="archiveModal"
        icon="ri-archive-line"
        icon-size="5rem"
        title="Are you sure you want to archive this item?"
        warning-text="This item will be moved to the archive."
        cancel-text="Keep Active"
        confirm-text="Yes, Archive"
        form-action="{{ route('admin.items.archive', $item) }}"
        form-method="PATCH"
    />
    
    With Dynamic Content:
    <x-confirmation-modal 
        modal-id="deleteUtilityModal"
        title="Are you sure you want to delete the utility &quot;<span id='utilityName' class='fw-semibold'></span>&quot;?"
        form-action="#"
        confirm-text="Yes, Delete Utility"
        target-element-id="utilityName"
    />
--}}

@props([
    'modalId' => 'confirmationModal',
    'icon' => 'bx bx-alert-circle',
    'iconSize' => '9rem',
    'title' => '',
    'warningText' => 'This action cannot be undone!',
    'cancelText' => 'No, Cancel',
    'confirmText' => 'Yes, Confirm',
    'formAction' => '#',
    'formMethod' => 'DELETE',
    'targetElementId' => null
])

<!-- Confirmation Modal -->
<div class="modal fade effect-scale" id="{{ $modalId }}" tabindex="-1" aria-labelledby="{{ $modalId }}Label" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0 pb-0 justify-content-center">
                <div class="text-center gap-3">
                    <i class="{{ $icon }} text-warning" style="font-size: {{ $iconSize }};"></i>
                </div>
                <button type="button" class="btn-close position-absolute" style="top: 15px; right: 15px;" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center pt-2">
                <p class="fs-6 mb-3">{!! $title !!}</p>
                <p class="text-danger fw-semi-bold mb-0">{{ $warningText }}</p>
            </div>
            <div class="modal-footer border-0 justify-content-center gap-3">
                <button type="button" class="btn btn-success" data-bs-dismiss="modal">{{ $cancelText }}</button>
                <form id="{{ $modalId }}Form" method="POST" action="{{ $formAction }}" style="display: inline;">
                    @csrf
                    @if(strtoupper($formMethod) !== 'POST')
                        @method($formMethod)
                    @endif
                    <button type="submit" class="btn btn-danger">{{ $confirmText }}</button>
                </form>
            </div>
        </div>
    </div>
</div>
