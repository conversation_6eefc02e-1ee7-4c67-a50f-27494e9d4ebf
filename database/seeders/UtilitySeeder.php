<?php

namespace Database\Seeders;

use App\Models\Utility;
use Illuminate\Database\Seeder;

class UtilitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $utilities = [
            [
                'name' => 'Stoel',
                'description' => 'Standard white chairs',
                'hourly_rate' => 1.50,
                'icon_class' => 'bx  bx-chair',
                'is_active' => true,
            ],
            [
                'name' => 'Mesa largu',
                'description' => 'Standard long white tables',
                'hourly_rate' => 5,
                'icon_class' => 'bx  bx-rectangle-wide',
                'is_active' => true,
            ],
            [
                'name' => 'Mesa rondo',
                'description' => 'Standard round white tables',
                'hourly_rate' => 15,
                'icon_class' => 'bx bx-circle',
                'is_active' => true,
            ],
            [
                'name' => 'Sta tafel',
                'description' => 'Standard standing table',
                'hourly_rate' => 15,
                'icon_class' => 'bx  bx-tent bx-rotate-180',
                'is_active' => true,
            ],
            [
                'name' => 'Paña di mesa',
                'description' => 'Standard white table cloth',
                'hourly_rate' => 15,
                'icon_class' => 'bx  bx-mesh',
                'is_active' => true,
            ],
        ];

        foreach ($utilities as $utility) {
            Utility::firstOrCreate(
                ['name' => $utility['name']],
                $utility
            );
        }
    }
}
