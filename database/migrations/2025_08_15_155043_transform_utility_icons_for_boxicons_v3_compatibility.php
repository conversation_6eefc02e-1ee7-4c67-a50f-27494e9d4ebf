<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Transforms utility icon classes for Boxicons v3 compatibility:
     * 1. Normalizes and fixes invalid icon classes
     * 2. Converts to complete CSS class format
     * 3. Handles all legacy formats in one step
     */
    public function up(): void
    {
        $utilities = DB::table('utilities')->get();

        foreach ($utilities as $utility) {
            $iconClass = $utility->icon_class;
            $transformedIconClass = $this->transformIconClass($iconClass);

            if ($iconClass !== $transformedIconClass) {
                DB::table('utilities')
                    ->where('id', $utility->id)
                    ->update(['icon_class' => $transformedIconClass]);
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * Converts back to simple icon name format
     */
    public function down(): void
    {
        $utilities = DB::table('utilities')->get();

        foreach ($utilities as $utility) {
            $iconClass = $utility->icon_class;
            $simpleIconClass = $this->convertToSimpleFormat($iconClass);

            if ($iconClass !== $simpleIconClass) {
                DB::table('utilities')
                    ->where('id', $utility->id)
                    ->update(['icon_class' => $simpleIconClass]);
            }
        }
    }

    /**
     * Transform icon class through complete normalization and format conversion
     *
     * Handles all legacy formats and converts to final Boxicons v3 compatible format:
     * - Fixes invalid classes (bx bxs-circle → bx bx-circle)
     * - Replaces non-existent icons (bx-closet → bx bx-cabinet)
     * - Normalizes redundant prefixes (bx bx-name → bx bx-name, bxl bxl-name → bxl bx-name)
     * - Converts to complete CSS format (bx-name → bx bx-name, bxl-name → bxl bx-name)
     */
    private function transformIconClass(?string $iconClass): string
    {
        if (! $iconClass) {
            return 'ri-tools-line';
        }

        $iconClass = trim($iconClass);

        // Step 1: Handle special cases and fix invalid classes
        if ($iconClass === 'bx bxs-circle' || $iconClass === 'bxs-circle') {
            return 'bx bx-circle'; // Use basic circle instead of solid
        }

        if ($iconClass === 'bx-closet') {
            return 'bx bx-cabinet'; // Use a valid Boxicons v3 icon
        }

        // Step 2: Normalize redundant class prefixes
        if (str_starts_with($iconClass, 'bx bx-')) {
            // Already in correct format for basic icons
            return $iconClass;
        }

        if (str_starts_with($iconClass, 'bxl bx-')) {
            // Already in correct format for brand icons
            return $iconClass;
        }

        if (str_starts_with($iconClass, 'bxl bxl-')) {
            // Fix redundant brand prefix: bxl bxl-name → bxl bx-name
            return 'bxl bx-'.substr($iconClass, 8);
        }

        // Step 3: Convert simple formats to complete CSS format
        if (str_starts_with($iconClass, 'bx-')) {
            // Basic icon: bx-name → bx bx-name
            return 'bx '.$iconClass;
        }

        if (str_starts_with($iconClass, 'bxl-')) {
            // Brand icon: bxl-name → bxl bx-name
            return 'bxl '.str_replace('bxl-', 'bx-', $iconClass);
        }

        // Step 4: Handle Remix icons and other formats (use as-is)
        if (str_starts_with($iconClass, 'ri-') || str_starts_with($iconClass, 'bxl ') || str_starts_with($iconClass, 'bx ')) {
            return $iconClass;
        }

        // Default fallback
        return $iconClass;
    }

    /**
     * Convert icon class back to simple format for rollback
     */
    private function convertToSimpleFormat(?string $iconClass): string
    {
        if (! $iconClass) {
            return 'ri-tools-line';
        }

        $iconClass = trim($iconClass);

        // Convert complete format back to simple format
        if (str_starts_with($iconClass, 'bx ')) {
            return substr($iconClass, 3); // Remove 'bx ' prefix
        }

        if (str_starts_with($iconClass, 'bxl ')) {
            if (str_starts_with($iconClass, 'bxl bx-')) {
                return 'bxl-'.substr($iconClass, 7); // Convert 'bxl bx-name' to 'bxl-name'
            }

            return substr($iconClass, 4); // Remove 'bxl ' prefix
        }

        // Return as-is for other formats (ri-*, etc.)
        return $iconClass;
    }
};
