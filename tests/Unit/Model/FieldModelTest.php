<?php

namespace Tests\Unit\Model;

use App\Models\Amenity;
use App\Models\Field;
use App\Models\Reservation;
use App\Models\Utility;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(Field::class)]
class FieldModelTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function field_has_correct_fillable_attributes()
    {
        $field = new Field;
        $fillable = $field->getFillable();

        $expectedFillable = [
            'name', 'type', 'description', 'hourly_rate', 'night_hourly_rate',
            'night_time_start', 'capacity', 'status', 'opening_time', 'closing_time',
            'min_booking_hours', 'max_booking_hours', 'start_date', 'end_date',
        ];

        foreach ($expectedFillable as $attribute) {
            $this->assertContains($attribute, $fillable);
        }
    }

    #[Test]
    public function field_has_correct_casts()
    {
        $field = Field::factory()->create([
            'hourly_rate' => '75.50',
            'night_hourly_rate' => '85.75',
            'min_booking_hours' => '1.5',
            'max_booking_hours' => '8.5',
        ]);

        // Test decimal casting
        $this->assertIsString($field->hourly_rate); // Laravel decimal cast returns string
        $this->assertEquals('75.50', $field->hourly_rate);
        $this->assertIsString($field->night_hourly_rate);
        $this->assertEquals('85.75', $field->night_hourly_rate);

        // Test decimal:1 casting for booking hours (supports half-hour increments)
        $this->assertIsString($field->min_booking_hours);
        $this->assertEquals('1.5', $field->min_booking_hours);
        $this->assertIsString($field->max_booking_hours);
        $this->assertEquals('8.5', $field->max_booking_hours);
    }

    #[Test]
    public function amenities_relationship_returns_belongs_to_many()
    {
        $field = Field::factory()->create();

        $relationship = $field->amenities();

        $this->assertInstanceOf(BelongsToMany::class, $relationship);
        $this->assertEquals('field_amenity', $relationship->getTable());
    }

    #[Test]
    public function amenities_relationship_returns_collection_of_amenity_objects()
    {
        $field = Field::factory()->create();
        $amenity1 = Amenity::factory()->create(['name' => 'Lighting']);
        $amenity2 = Amenity::factory()->create(['name' => 'Parking']);

        // Attach amenities to field
        $field->amenities()->attach([$amenity1->id, $amenity2->id]);

        $amenities = $field->amenities;

        $this->assertInstanceOf(Collection::class, $amenities);
        $this->assertCount(2, $amenities);
        $this->assertInstanceOf(Amenity::class, $amenities->first());
        $this->assertTrue($amenities->contains($amenity1));
        $this->assertTrue($amenities->contains($amenity2));
    }

    #[Test]
    public function amenities_collection_contains_proper_amenity_objects_not_arrays()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create([
            'name' => 'Test Amenity',
            'icon_class' => 'ri-test-line',
            'is_active' => true,
        ]);

        $field->amenities()->attach($amenity->id);
        $amenities = $field->amenities;

        $firstAmenity = $amenities->first();

        // Verify it's an Amenity object with proper properties
        $this->assertInstanceOf(Amenity::class, $firstAmenity);
        $this->assertEquals('Test Amenity', $firstAmenity->name);
        $this->assertEquals('ri-test-line', $firstAmenity->icon_class);
        $this->assertTrue($firstAmenity->is_active);

        // Verify it's NOT an array
        $this->assertIsNotArray($firstAmenity);

        // Verify object properties are accessible via magic methods
        $this->assertIsString($firstAmenity->name);
        $this->assertIsString($firstAmenity->icon_class);
        $this->assertIsBool($firstAmenity->is_active);
    }

    #[Test]
    public function get_available_amenities_returns_proper_array_structure()
    {
        // Create some amenities
        $amenity1 = Amenity::factory()->create(['name' => 'Lighting', 'is_active' => true]);
        $amenity2 = Amenity::factory()->create(['name' => 'Parking', 'is_active' => true]);
        $amenity3 = Amenity::factory()->create(['name' => 'Inactive', 'is_active' => false]);

        $availableAmenities = Field::getAvailableAmenities();

        // Should be an array
        $this->assertIsArray($availableAmenities);

        // Should have ID as key and name as value for active amenities
        $this->assertArrayHasKey($amenity1->id, $availableAmenities);
        $this->assertArrayHasKey($amenity2->id, $availableAmenities);
        $this->assertEquals('Lighting', $availableAmenities[$amenity1->id]);
        $this->assertEquals('Parking', $availableAmenities[$amenity2->id]);

        // Should NOT include inactive amenities
        $this->assertArrayNotHasKey($amenity3->id, $availableAmenities);
    }

    #[Test]
    public function get_available_amenities_handles_database_exception()
    {
        // This test ensures the method works normally when database is available
        // Create some amenities first
        $amenity1 = Amenity::factory()->create(['name' => 'Lighting', 'is_active' => true]);
        $amenity2 = Amenity::factory()->create(['name' => 'Parking', 'is_active' => true]);

        $availableAmenities = Field::getAvailableAmenities();

        // Should return an array with ID keys and name values
        $this->assertIsArray($availableAmenities);
        $this->assertArrayHasKey($amenity1->id, $availableAmenities);
        $this->assertArrayHasKey($amenity2->id, $availableAmenities);
        $this->assertEquals('Lighting', $availableAmenities[$amenity1->id]);
        $this->assertEquals('Parking', $availableAmenities[$amenity2->id]);
    }

    #[Test]
    public function get_formatted_amenities_attribute_returns_correct_string()
    {
        $field = Field::factory()->create();

        // Test with no amenities
        $this->assertEquals('None', $field->formatted_amenities);

        // Test with amenities
        $amenity1 = Amenity::factory()->create(['name' => 'Lighting']);
        $amenity2 = Amenity::factory()->create(['name' => 'Parking']);
        $field->amenities()->attach([$amenity1->id, $amenity2->id]);
        $field->refresh();

        $formattedAmenities = $field->formatted_amenities;
        $this->assertIsString($formattedAmenities);
        $this->assertStringContainsString('Lighting', $formattedAmenities);
        $this->assertStringContainsString('Parking', $formattedAmenities);
        $this->assertStringContainsString(', ', $formattedAmenities);
    }

    #[Test]
    public function formatted_amenities_handles_empty_collection()
    {
        $field = Field::factory()->create();

        // Ensure no amenities are attached
        $this->assertEquals(0, $field->amenities->count());

        // Should return 'None'
        $this->assertEquals('None', $field->formatted_amenities);
    }

    #[Test]
    public function active_scope_returns_only_active_fields()
    {
        Field::factory()->create(['status' => 'Active', 'name' => 'Active Field']);
        Field::factory()->create(['status' => 'Inactive', 'name' => 'Inactive Field']);
        Field::factory()->create(['status' => 'Under Maintenance', 'name' => 'Maintenance Field']);

        $activeFields = Field::active()->get();

        $this->assertEquals(1, $activeFields->count());
        $this->assertEquals('Active', $activeFields->first()->status);
        $this->assertEquals('Active Field', $activeFields->first()->name);
    }

    #[Test]
    public function available_scope_returns_available_fields()
    {
        Field::factory()->create(['status' => 'Active', 'name' => 'Active Field']);
        Field::factory()->create(['status' => 'Inactive', 'name' => 'Inactive Field']);
        Field::factory()->create(['status' => 'Under Maintenance', 'name' => 'Maintenance Field']);

        $availableFields = Field::available()->get();

        $this->assertEquals(1, $availableFields->count());
        $this->assertEquals('Active', $availableFields->first()->status);
        $this->assertEquals('Active Field', $availableFields->first()->name);
    }

    #[Test]
    public function field_amenities_cannot_be_accessed_with_array_syntax()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create(['name' => 'Test Amenity']);
        $field->amenities()->attach($amenity->id);

        $amenities = $field->amenities;
        $firstAmenity = $amenities->first();

        // This should work (object property access)
        $this->assertEquals('Test Amenity', $firstAmenity->name);

        // Test that array access doesn't work by checking if it's not an array
        // In modern PHP, trying to access object properties with array syntax
        // might not always throw an exception, so we'll test the type instead
        $this->assertFalse(is_array($firstAmenity));
        $this->assertTrue(is_object($firstAmenity));

        // Verify that proper object access works
        $this->assertIsString($firstAmenity->name);
        $this->assertEquals('Test Amenity', $firstAmenity->name);
    }

    #[Test]
    public function amenity_objects_in_collection_are_not_arrays()
    {
        $field = Field::factory()->create();
        $amenity1 = Amenity::factory()->create(['name' => 'Lighting']);
        $amenity2 = Amenity::factory()->create(['name' => 'Parking']);
        $field->amenities()->attach([$amenity1->id, $amenity2->id]);

        $amenities = $field->amenities;

        foreach ($amenities as $amenity) {
            // Each amenity should be an object, not an array
            $this->assertIsObject($amenity);
            $this->assertInstanceOf(Amenity::class, $amenity);
            $this->assertIsNotArray($amenity);

            // Should have object properties
            $this->assertIsString($amenity->name);
            $this->assertIsString($amenity->icon_class);
            $this->assertIsBool($amenity->is_active);
        }
    }

    #[Test]
    public function field_amenities_relationship_pivot_table_works()
    {
        $field = Field::factory()->create();
        $amenity = Amenity::factory()->create();

        // Test attaching
        $field->amenities()->attach($amenity->id);
        $this->assertTrue($field->amenities->contains($amenity));

        // Test detaching
        $field->amenities()->detach($amenity->id);
        $field->refresh();
        $this->assertFalse($field->amenities->contains($amenity));

        // Test sync
        $amenity2 = Amenity::factory()->create();
        $field->amenities()->sync([$amenity->id, $amenity2->id]);
        $field->refresh();
        $this->assertEquals(2, $field->amenities->count());
    }

    #[Test]
    public function field_can_have_multiple_amenities_and_amenity_can_have_multiple_fields()
    {
        $field1 = Field::factory()->create(['name' => 'Field 1']);
        $field2 = Field::factory()->create(['name' => 'Field 2']);
        $amenity = Amenity::factory()->create(['name' => 'Shared Amenity']);

        // Attach same amenity to both fields
        $field1->amenities()->attach($amenity->id);
        $field2->amenities()->attach($amenity->id);

        // Verify many-to-many relationship
        $this->assertTrue($field1->amenities->contains($amenity));
        $this->assertTrue($field2->amenities->contains($amenity));
        $this->assertTrue($amenity->fields->contains($field1));
        $this->assertTrue($amenity->fields->contains($field2));
        $this->assertEquals(2, $amenity->fields->count());
    }

    #[Test]
    public function field_has_soft_deletes_trait()
    {
        $field = Field::factory()->create(['name' => 'Test Field']);
        $fieldId = $field->id;

        // Soft delete the field
        $field->delete();

        // Verify field is soft deleted
        $this->assertSoftDeleted('fields', ['id' => $fieldId]);
        $this->assertNotNull($field->fresh()->deleted_at);

        // Verify field is not included in normal queries
        $this->assertNull(Field::find($fieldId));

        // Verify field can be found with trashed
        $this->assertNotNull(Field::withTrashed()->find($fieldId));
    }

    #[Test]
    public function field_uses_has_factory_trait()
    {
        $this->assertTrue(method_exists(Field::class, 'factory'));

        $field = Field::factory()->create();
        $this->assertInstanceOf(Field::class, $field);
        $this->assertNotNull($field->id);
    }

    #[Test]
    public function field_has_correct_table_name()
    {
        $field = new Field;
        $this->assertEquals('fields', $field->getTable());
    }

    #[Test]
    public function field_has_correct_primary_key()
    {
        $field = new Field;
        $this->assertEquals('id', $field->getKeyName());
        $this->assertTrue($field->getIncrementing());
        $this->assertEquals('int', $field->getKeyType());
    }

    #[Test]
    public function field_has_timestamps()
    {
        $field = Field::factory()->create();

        $this->assertNotNull($field->created_at);
        $this->assertNotNull($field->updated_at);
        $this->assertInstanceOf(Carbon::class, $field->created_at);
        $this->assertInstanceOf(Carbon::class, $field->updated_at);
    }

    #[Test]
    public function field_mass_assignment_protection_works()
    {
        $field = new Field;

        // Test that only fillable attributes can be mass assigned
        $field->fill([
            'name' => 'Test Field',
            'type' => 'Soccer',
            'hourly_rate' => 50.00,
            'id' => 999, // Should be ignored
            'created_at' => now(), // Should be ignored
        ]);

        $this->assertEquals('Test Field', $field->name);
        $this->assertEquals('Soccer', $field->type);
        $this->assertEquals(50.00, $field->hourly_rate);
        $this->assertNull($field->id); // Should not be set
        $this->assertNull($field->created_at); // Should not be set
    }

    #[Test]
    public function field_can_be_serialized_to_array()
    {
        $field = Field::factory()->create([
            'name' => 'Test Field',
            'type' => 'Soccer',
            'hourly_rate' => 75.50,
        ]);

        $array = $field->toArray();

        $this->assertIsArray($array);
        $this->assertEquals('Test Field', $array['name']);
        $this->assertEquals('Soccer', $array['type']);
        $this->assertEquals('75.50', $array['hourly_rate']);
        $this->assertArrayHasKey('id', $array);
        $this->assertArrayHasKey('created_at', $array);
        $this->assertArrayHasKey('updated_at', $array);
    }

    #[Test]
    public function field_can_be_serialized_to_json()
    {
        $field = Field::factory()->create([
            'name' => 'Test Field',
            'type' => 'Soccer',
        ]);

        $json = $field->toJson();
        $decoded = json_decode($json, true);

        $this->assertIsString($json);
        $this->assertIsArray($decoded);
        $this->assertEquals('Test Field', $decoded['name']);
        $this->assertEquals('Soccer', $decoded['type']);
    }

    #[Test]
    public function get_field_types_returns_correct_array()
    {
        $fieldTypes = Field::getFieldTypes();

        $expectedTypes = [
            'Soccer' => 'Soccer',
            'Basketball' => 'Basketball',
            'Tennis' => 'Tennis',
            'Volleyball' => 'Volleyball',
            'Baseball' => 'Baseball',
            'Football' => 'Football',
            'Multi-Purpose' => 'Multi-Purpose',
        ];

        $this->assertIsArray($fieldTypes);
        $this->assertEquals($expectedTypes, $fieldTypes);
    }

    #[Test]
    public function get_statuses_returns_correct_array()
    {
        $statuses = Field::getStatuses();

        $expectedStatuses = [
            'Active' => 'Active',
            'Inactive' => 'Inactive',
            // Note: 'Under Maintenance' is commented out in the current implementation
        ];

        $this->assertIsArray($statuses);
        $this->assertEquals($expectedStatuses, $statuses);
    }

    #[Test]
    public function get_available_amenities_legacy_returns_correct_array()
    {
        $amenities = Field::getAvailableAmenitiesLegacy();

        $expectedAmenities = [
            'lighting' => 'Lighting',
            'parking' => 'Parking',
            'restrooms' => 'Restrooms',
            'equipment' => 'Equipment Available',
            'scoreboard' => 'Scoreboard',
            'seating' => 'Spectator Seating',
            'sound_system' => 'Sound System',
            'wifi' => 'WiFi',
        ];

        $this->assertIsArray($amenities);
        $this->assertEquals($expectedAmenities, $amenities);
    }

    #[Test]
    public function bookings_relationship_returns_has_many()
    {
        $field = Field::factory()->create();

        $relationship = $field->bookings();

        $this->assertInstanceOf(HasMany::class, $relationship);
        $this->assertEquals('field_id', $relationship->getForeignKeyName());
        $this->assertEquals('id', $relationship->getLocalKeyName());
    }

    #[Test]
    public function reservations_relationship_returns_has_many()
    {
        $field = Field::factory()->create();

        $relationship = $field->reservations();

        $this->assertInstanceOf(HasMany::class, $relationship);
        $this->assertEquals('field_id', $relationship->getForeignKeyName());
        $this->assertEquals('id', $relationship->getLocalKeyName());
    }

    #[Test]
    public function utilities_relationship_returns_belongs_to_many()
    {
        $field = Field::factory()->create();

        $relationship = $field->utilities();

        $this->assertInstanceOf(BelongsToMany::class, $relationship);
        $this->assertEquals('field_utility', $relationship->getTable());
    }

    #[Test]
    public function utilities_relationship_has_timestamps()
    {
        $field = Field::factory()->create();
        $utility = Utility::factory()->create();

        $field->utilities()->attach($utility->id);

        $attachedUtility = $field->utilities->first();
        $this->assertNotNull($attachedUtility->pivot->created_at);
        $this->assertNotNull($attachedUtility->pivot->updated_at);
    }

    #[Test]
    public function field_can_have_multiple_utilities()
    {
        $field = Field::factory()->create();
        $utility1 = Utility::factory()->create(['name' => 'Utility 1']);
        $utility2 = Utility::factory()->create(['name' => 'Utility 2']);

        $field->utilities()->attach([$utility1->id, $utility2->id]);

        $this->assertEquals(2, $field->utilities->count());
        $this->assertTrue($field->utilities->contains($utility1));
        $this->assertTrue($field->utilities->contains($utility2));
    }

    #[Test]
    public function active_bookings_relationship_returns_has_many_with_scope()
    {
        $field = Field::factory()->create();

        $relationship = $field->activeBookings();

        $this->assertInstanceOf(HasMany::class, $relationship);

        // Check that the query has the correct where clause
        $sql = $relationship->toSql();
        $this->assertStringContainsString('status', $sql);
    }

    #[Test]
    public function field_can_have_reservations()
    {
        $field = Field::factory()->create();
        $reservation1 = Reservation::factory()->create(['field_id' => $field->id]);
        $reservation2 = Reservation::factory()->create(['field_id' => $field->id]);

        $reservations = $field->reservations;

        $this->assertInstanceOf(Collection::class, $reservations);
        $this->assertEquals(2, $reservations->count());
        $this->assertTrue($reservations->contains($reservation1));
        $this->assertTrue($reservations->contains($reservation2));
    }

    #[Test]
    public function field_utilities_many_to_many_relationship_works()
    {
        $field1 = Field::factory()->create(['name' => 'Field 1']);
        $field2 = Field::factory()->create(['name' => 'Field 2']);
        $utility = Utility::factory()->create(['name' => 'Shared Utility']);

        // Attach same utility to both fields
        $field1->utilities()->attach($utility->id);
        $field2->utilities()->attach($utility->id);

        // Verify many-to-many relationship
        $this->assertTrue($field1->utilities->contains($utility));
        $this->assertTrue($field2->utilities->contains($utility));
        $this->assertTrue($utility->fields->contains($field1));
        $this->assertTrue($utility->fields->contains($field2));
        $this->assertEquals(2, $utility->fields->count());
    }

    #[Test]
    public function field_utilities_pivot_operations_work()
    {
        $field = Field::factory()->create();
        $utility = Utility::factory()->create();

        // Test attaching
        $field->utilities()->attach($utility->id);
        $this->assertTrue($field->utilities->contains($utility));

        // Test detaching
        $field->utilities()->detach($utility->id);
        $field->refresh();
        $this->assertFalse($field->utilities->contains($utility));

        // Test sync
        $utility2 = Utility::factory()->create();
        $field->utilities()->sync([$utility->id, $utility2->id]);
        $field->refresh();
        $this->assertEquals(2, $field->utilities->count());
    }

    #[Test]
    public function is_available_at_returns_true_when_no_conflicting_bookings()
    {
        $field = Field::factory()->create();
        $date = '2024-01-15';
        $startTime = '10:00';
        $endTime = '12:00';

        $result = $field->isAvailableAt($date, $startTime, $endTime);

        $this->assertTrue($result);
    }

    #[Test]
    public function is_available_at_returns_false_when_conflicting_booking_exists()
    {
        $field = Field::factory()->create();
        $date = '2024-01-15';

        // Create a conflicting reservation
        Reservation::factory()->create([
            'field_id' => $field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Test overlapping time slot
        $result = $field->isAvailableAt($date, '11:00', '13:00');

        $this->assertFalse($result);
    }

    #[Test]
    public function is_available_at_ignores_cancelled_bookings()
    {
        $field = Field::factory()->create();
        $date = '2024-01-15';

        // Create a cancelled reservation
        Reservation::factory()->create([
            'field_id' => $field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Cancelled',
        ]);

        // Should be available since booking is cancelled
        $result = $field->isAvailableAt($date, '11:00', '13:00');

        $this->assertTrue($result);
    }

    #[Test]
    public function is_available_at_excludes_specified_booking_id()
    {
        $field = Field::factory()->create();
        $date = '2024-01-15';

        // Create a reservation
        $reservation = Reservation::factory()->create([
            'field_id' => $field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Should be available when excluding this booking
        $result = $field->isAvailableAt($date, '11:00', '13:00', $reservation->id);

        $this->assertTrue($result);
    }

    #[Test]
    public function is_within_working_hours_returns_true_for_valid_times()
    {
        $field = Field::factory()->create([
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);

        $result = $field->isWithinWorkingHours('10:00', '12:00');

        $this->assertTrue($result);
    }

    #[Test]
    public function is_within_working_hours_returns_false_for_early_start()
    {
        $field = Field::factory()->create([
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);

        $result = $field->isWithinWorkingHours('07:00', '09:00');

        $this->assertFalse($result);
    }

    #[Test]
    public function is_within_working_hours_returns_false_for_late_end()
    {
        $field = Field::factory()->create([
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);

        $result = $field->isWithinWorkingHours('20:00', '23:00');

        $this->assertFalse($result);
    }

    #[Test]
    public function is_within_working_hours_returns_false_for_overnight_booking()
    {
        $field = Field::factory()->create([
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);

        $result = $field->isWithinWorkingHours('23:00', '01:00');

        $this->assertFalse($result);
    }

    #[Test]
    public function is_valid_duration_returns_true_for_valid_hours()
    {
        $field = Field::factory()->create([
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
        ]);

        $this->assertTrue($field->isValidDuration(1));
        $this->assertTrue($field->isValidDuration(4));
        $this->assertTrue($field->isValidDuration(8));
    }

    #[Test]
    public function is_valid_duration_returns_false_for_invalid_hours()
    {
        $field = Field::factory()->create([
            'min_booking_hours' => 2,
            'max_booking_hours' => 6,
        ]);

        $this->assertFalse($field->isValidDuration(1)); // Too short
        $this->assertFalse($field->isValidDuration(7)); // Too long
    }

    #[Test]
    public function get_available_time_slots_returns_array_of_slots()
    {
        $field = Field::factory()->create([
            'opening_time' => '08:00',
            'closing_time' => '10:00', // Short window for testing
        ]);
        $date = '2024-01-15';

        $slots = $field->getAvailableTimeSlots($date);

        $this->assertIsArray($slots);
        $this->assertCount(4, $slots); // 30-minute slots: 08:00-08:30, 08:30-09:00, 09:00-09:30, 09:30-10:00

        $this->assertEquals('08:00', $slots[0]['start_time']);
        $this->assertEquals('08:30', $slots[0]['end_time']);
        $this->assertEquals('8:00 AM', $slots[0]['display']);

        $this->assertEquals('08:30', $slots[1]['start_time']);
        $this->assertEquals('09:00', $slots[1]['end_time']);
        $this->assertEquals('8:30 AM', $slots[1]['display']);

        $this->assertEquals('09:00', $slots[2]['start_time']);
        $this->assertEquals('09:30', $slots[2]['end_time']);
        $this->assertEquals('9:00 AM', $slots[2]['display']);

        $this->assertEquals('09:30', $slots[3]['start_time']);
        $this->assertEquals('10:00', $slots[3]['end_time']);
        $this->assertEquals('9:30 AM', $slots[3]['display']);
    }

    #[Test]
    public function is_available_returns_true_when_no_conflicting_bookings()
    {
        $field = Field::factory()->create();
        $date = '2024-01-15';
        $startTime = '10:00';
        $endTime = '12:00';

        $result = $field->isAvailable($date, $startTime, $endTime);

        $this->assertTrue($result);
    }

    #[Test]
    public function is_available_returns_false_when_conflicting_booking_exists()
    {
        $field = Field::factory()->create();
        $date = '2024-01-15';

        // Create a conflicting reservation
        Reservation::factory()->create([
            'field_id' => $field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Test overlapping time slot
        $result = $field->isAvailable($date, '11:00', '13:00');

        $this->assertFalse($result);
    }

    #[Test]
    public function is_available_only_checks_pending_and_confirmed_bookings()
    {
        $field = Field::factory()->create();
        $date = '2024-01-15';

        // Create a cancelled reservation
        Reservation::factory()->create([
            'field_id' => $field->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Cancelled',
        ]);

        // Should be available since booking is cancelled
        $result = $field->isAvailable($date, '11:00', '13:00');

        $this->assertTrue($result);
    }

    #[Test]
    public function is_night_time_returns_false_when_no_night_time_start()
    {
        // Create field and manually set night_time_start to null after creation
        $field = Field::factory()->create();
        $field->night_time_start = null;

        $result = $field->isNightTime('20:00');

        $this->assertFalse($result);
    }

    #[Test]
    public function is_night_time_returns_true_for_night_hours()
    {
        $field = Field::factory()->create(['night_time_start' => '18:00']);

        $this->assertTrue($field->isNightTime('19:00'));
        $this->assertTrue($field->isNightTime('22:00'));
        $this->assertFalse($field->isNightTime('17:00'));
        $this->assertFalse($field->isNightTime('10:00'));
    }

    #[Test]
    public function is_night_time_handles_midnight_spanning_hours()
    {
        // Test with night start at 22:00 (night time spans midnight until 6 AM)
        $field = Field::factory()->create(['night_time_start' => '22:00']);

        $this->assertTrue($field->isNightTime('23:00'));  // Night time
        $this->assertTrue($field->isNightTime('01:00'));  // Night time continues past midnight
        $this->assertTrue($field->isNightTime('05:00'));  // Still night time before 6 AM
        $this->assertFalse($field->isNightTime('21:00')); // Before night start
        $this->assertFalse($field->isNightTime('14:00')); // Day time
        $this->assertFalse($field->isNightTime('06:00')); // Day starts at 6 AM
        $this->assertFalse($field->isNightTime('07:00')); // Day time
    }

    #[Test]
    public function is_night_time_handles_early_morning_night_start()
    {
        // Test with night start at 02:00 (unusual but possible - night from 2 AM to 6 AM)
        $field = Field::factory()->create(['night_time_start' => '02:00']);

        $this->assertTrue($field->isNightTime('03:00'));  // After night start (03:00 >= 02:00)
        $this->assertTrue($field->isNightTime('05:00'));  // Still within night period
        $this->assertFalse($field->isNightTime('10:00')); // Day time (after 6 AM)
        $this->assertFalse($field->isNightTime('01:00')); // Before night start
        $this->assertFalse($field->isNightTime('14:00')); // Day time
        $this->assertFalse($field->isNightTime('06:00')); // Night ends at 6 AM
        $this->assertFalse($field->isNightTime('23:00')); // Day time (before 2 AM start)
    }

    #[Test]
    public function get_hourly_rate_for_time_returns_day_rate_during_day()
    {
        $field = Field::factory()->create([
            'hourly_rate' => 50.00,
            'night_hourly_rate' => 75.00,
            'night_time_start' => '18:00',
        ]);

        $rate = $field->getHourlyRateForTime('15:00');

        $this->assertEquals(50.00, $rate);
    }

    #[Test]
    public function get_hourly_rate_for_time_returns_night_rate_during_night()
    {
        $field = Field::factory()->create([
            'hourly_rate' => 50.00,
            'night_hourly_rate' => 75.00,
            'night_time_start' => '18:00',
        ]);

        $rate = $field->getHourlyRateForTime('20:00');

        $this->assertEquals(75.00, $rate);
    }

    #[Test]
    public function get_hourly_rate_for_time_returns_day_rate_when_no_night_rate()
    {
        $field = Field::factory()->create([
            'hourly_rate' => 50.00,
            'night_hourly_rate' => null,
            'night_time_start' => '18:00',
        ]);

        $rate = $field->getHourlyRateForTime('20:00');

        $this->assertEquals(50.00, $rate);
    }

    #[Test]
    public function calculate_booking_cost_calculates_correctly_for_day_hours()
    {
        $field = Field::factory()->create([
            'hourly_rate' => 50.00,
            'night_hourly_rate' => 75.00,
            'night_time_start' => '18:00',
        ]);

        $cost = $field->calculateBookingCost('10:00', '12:00');

        $this->assertEquals(100.00, $cost); // 2 hours * 50.00
    }

    #[Test]
    public function calculate_booking_cost_calculates_correctly_for_night_hours()
    {
        $field = Field::factory()->create([
            'hourly_rate' => 50.00,
            'night_hourly_rate' => 75.00,
            'night_time_start' => '18:00',
        ]);

        $cost = $field->calculateBookingCost('19:00', '21:00');

        $this->assertEquals(150.00, $cost); // 2 hours * 75.00
    }

    #[Test]
    public function calculate_booking_cost_calculates_correctly_for_mixed_hours()
    {
        $field = Field::factory()->create([
            'hourly_rate' => 50.00,
            'night_hourly_rate' => 75.00,
            'night_time_start' => '18:00',
        ]);

        $cost = $field->calculateBookingCost('17:00', '19:00');

        $this->assertEquals(125.00, $cost); // 1 hour * 50.00 + 1 hour * 75.00
    }

    #[Test]
    public function get_formatted_day_rate_attribute_returns_formatted_string()
    {
        $field = Field::factory()->create(['hourly_rate' => 75.50]);

        $formattedRate = $field->formatted_day_rate;

        $this->assertEquals('XCG 75.50', $formattedRate);
    }

    #[Test]
    public function get_formatted_night_rate_attribute_returns_formatted_string()
    {
        $field = Field::factory()->create(['night_hourly_rate' => 85.75]);

        $formattedRate = $field->formatted_night_rate;

        $this->assertEquals('XCG 85.75', $formattedRate);
    }

    #[Test]
    public function get_formatted_night_rate_attribute_returns_default_when_null()
    {
        $field = Field::factory()->create(['night_hourly_rate' => null]);

        $formattedRate = $field->formatted_night_rate;

        $this->assertEquals('Same as day rate', $formattedRate);
    }

    #[Test]
    public function get_formatted_night_time_start_attribute_returns_formatted_time()
    {
        $field = Field::factory()->create(['night_time_start' => '18:00']);

        $formattedTime = $field->formatted_night_time_start;

        $this->assertEquals('6:00 PM', $formattedTime);
    }

    #[Test]
    public function get_formatted_night_time_start_attribute_returns_default_when_null()
    {
        $field = Field::factory()->create();
        $field->night_time_start = null;

        $formattedTime = $field->formatted_night_time_start;

        $this->assertEquals('6:00 PM', $formattedTime);
    }

    #[Test]
    public function get_formatted_night_time_start_attribute_handles_datetime_string()
    {
        $field = Field::factory()->create(['night_time_start' => '2024-01-01 20:30:00']);

        $formattedTime = $field->formatted_night_time_start;

        $this->assertEquals('8:30 PM', $formattedTime);
    }

    #[Test]
    public function field_factory_creates_valid_field()
    {
        $field = Field::factory()->create();

        $this->assertInstanceOf(Field::class, $field);
        $this->assertNotNull($field->name);
        $this->assertNotNull($field->type);
        $this->assertNotNull($field->hourly_rate);
        $this->assertNotNull($field->capacity);
        $this->assertNotNull($field->status);
        $this->assertIsNumeric($field->hourly_rate);
        $this->assertIsInt($field->capacity);
        $this->assertContains($field->status, ['Active', 'Inactive', 'Under Maintenance']);
    }

    #[Test]
    public function field_factory_states_work_correctly()
    {
        $activeField = Field::factory()->active()->create();
        $this->assertEquals('Active', $activeField->status);

        $inactiveField = Field::factory()->inactive()->create();
        $this->assertEquals('Inactive', $inactiveField->status);

        $maintenanceField = Field::factory()->underMaintenance()->create();
        $this->assertEquals('Under Maintenance', $maintenanceField->status);

        $soccerField = Field::factory()->soccer()->create();
        $this->assertEquals('Soccer', $soccerField->type);

        $basketballField = Field::factory()->basketball()->create();
        $this->assertEquals('Basketball', $basketballField->type);

        $tennisField = Field::factory()->tennis()->create();
        $this->assertEquals('Tennis', $tennisField->type);
    }

    #[Test]
    public function field_can_be_created_with_all_fillable_attributes()
    {
        $fieldData = [
            'name' => 'Test Field',
            'type' => 'Soccer',
            'description' => 'A test soccer field',
            'hourly_rate' => 75.50,
            'night_hourly_rate' => 85.75,
            'night_time_start' => '18:00',
            'capacity' => 22,
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
            'min_booking_hours' => 1.5,
            'max_booking_hours' => 8.5,
            'start_date' => '2024-06-01',
            'end_date' => '2024-06-15',
        ];

        $field = Field::create($fieldData);

        $this->assertInstanceOf(Field::class, $field);
        $this->assertEquals('Test Field', $field->name);
        $this->assertEquals('Soccer', $field->type);
        $this->assertEquals('A test soccer field', $field->description);
        $this->assertEquals('75.50', $field->hourly_rate);
        $this->assertEquals('85.75', $field->night_hourly_rate);
        $this->assertEquals('18:00', $field->night_time_start);
        $this->assertEquals(22, $field->capacity);
        $this->assertEquals('Active', $field->status);
        $this->assertEquals('08:00', $field->opening_time);
        $this->assertEquals('22:00', $field->closing_time);
        $this->assertEquals('1.5', $field->min_booking_hours);
        $this->assertEquals('8.5', $field->max_booking_hours);
        $this->assertEquals('2024-06-01', $field->start_date);
        $this->assertEquals('2024-06-15', $field->end_date);
    }

    #[Test]
    public function field_decimal_casting_works_correctly()
    {
        $field = Field::factory()->create([
            'hourly_rate' => 75.5,
            'night_hourly_rate' => 85.75,
        ]);

        // Test that decimal casting returns string with 2 decimal places
        $this->assertIsString($field->hourly_rate);
        $this->assertIsString($field->night_hourly_rate);
        $this->assertEquals('75.50', $field->hourly_rate);
        $this->assertEquals('85.75', $field->night_hourly_rate);

        // Test that raw values are still numeric
        $this->assertIsNumeric($field->getRawOriginal('hourly_rate'));
        $this->assertIsNumeric($field->getRawOriginal('night_hourly_rate'));
    }

    #[Test]
    public function field_model_has_correct_database_connection()
    {
        $field = new Field;

        // Should use default connection
        $this->assertNull($field->getConnectionName());
    }

    #[Test]
    public function field_model_uses_correct_date_format()
    {
        $field = Field::factory()->create();

        // Test that timestamps are properly formatted
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $field->created_at->toDateTimeString());
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $field->updated_at->toDateTimeString());
    }

    #[Test]
    public function field_model_handles_null_values_correctly()
    {
        $field = Field::factory()->create([
            'description' => null,
            'night_hourly_rate' => null,
        ]);

        // Manually set night_time_start to null after creation due to NOT NULL constraint
        $field->night_time_start = null;

        $this->assertNull($field->description);
        $this->assertNull($field->night_hourly_rate);
        $this->assertNull($field->night_time_start);
    }

    #[Test]
    public function update_status_based_on_dates_sets_under_maintenance_when_in_maintenance_period()
    {
        $today = Carbon::today();
        $startDate = $today->copy()->subDays(2);
        $endDate = $today->copy()->addDays(2);

        $field = Field::factory()->create([
            'status' => 'Active',
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);

        $field->updateStatusBasedOnDates();

        $this->assertEquals('Under Maintenance', $field->status);
    }

    #[Test]
    public function update_status_based_on_dates_sets_active_when_outside_maintenance_period()
    {
        $today = Carbon::today();
        $startDate = $today->copy()->addDays(5);
        $endDate = $today->copy()->addDays(10);

        $field = Field::factory()->create([
            'status' => 'Under Maintenance',
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);

        $field->updateStatusBasedOnDates();

        $this->assertEquals('Active', $field->status);
    }

    #[Test]
    public function update_status_based_on_dates_does_not_change_inactive_fields()
    {
        $today = Carbon::today();
        $startDate = $today->copy()->subDays(2);
        $endDate = $today->copy()->addDays(2);

        $field = Field::factory()->create([
            'status' => 'Inactive',
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);

        $field->updateStatusBasedOnDates();

        $this->assertEquals('Inactive', $field->status);
    }

    #[Test]
    public function update_status_based_on_dates_sets_active_when_no_maintenance_dates()
    {
        $field = Field::factory()->create([
            'status' => 'Under Maintenance',
            'start_date' => null,
            'end_date' => null,
        ]);

        $field->updateStatusBasedOnDates();

        $this->assertEquals('Active', $field->status);
    }

    #[Test]
    public function update_status_based_on_dates_handles_edge_case_dates()
    {
        $today = Carbon::today();

        // Test when today is exactly the start date
        $field1 = Field::factory()->create([
            'status' => 'Active',
            'start_date' => $today,
            'end_date' => $today->copy()->addDays(5),
        ]);
        $field1->updateStatusBasedOnDates();
        $this->assertEquals('Under Maintenance', $field1->status);

        // Test when today is exactly the end date
        $field2 = Field::factory()->create([
            'status' => 'Active',
            'start_date' => $today->copy()->subDays(5),
            'end_date' => $today,
        ]);
        $field2->updateStatusBasedOnDates();
        $this->assertEquals('Under Maintenance', $field2->status);
    }
}
