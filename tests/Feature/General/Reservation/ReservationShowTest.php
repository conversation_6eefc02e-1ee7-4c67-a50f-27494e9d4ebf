<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ReservationShowTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function user_can_view_reservation_show_page_after_creation()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        $field = Field::factory()->create([
            'opening_time' => '08:00:00',
            'closing_time' => '22:00:00',
            'status' => 'Active',
        ]);

        $reservation = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $user->id,
            'booking_date' => now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'end_time' => '11:00',
            'duration_hours' => 1.0,
            'total_cost' => 50.00,
            'status' => 'Confirmed',
        ]);

        $response = $this->get(route('reservations.show', $reservation));
        $response->assertStatus(200);
        $response->assertSeeText('Reservation Details');
        $response->assertSeeText((string) $reservation->id);
        $response->assertSeeText('Total Cost');
    }
}
