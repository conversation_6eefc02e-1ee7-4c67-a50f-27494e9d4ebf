<?php

namespace Tests\Feature;

use App\Models\Amenity;
use App\Models\Field;
use Database\Seeders\AmenitySeeder;
use Database\Seeders\FPMPFieldSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\Database\Seeders\FPMPFieldSeeder::class)]
class FPMPFieldSeederTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function fpmp_field_seeder_creates_fields_with_amenities_successfully()
    {
        // First seed amenities
        $this->seed(AmenitySeeder::class);

        // Then seed FPMP fields
        $this->seed(FPMPFieldSeeder::class);

        // Assert FPMP fields were created
        $this->assertDatabaseCount('fields', 4);

        // Assert specific FPMP fields exist with all seeder-defined properties
        $this->assertDatabaseHas('fields', [
            'name' => 'Veld futbol',
            'type' => 'Soccer',
            'hourly_rate' => 50.00,
            'night_hourly_rate' => 80.00,
            'night_time_start' => '18:30',
            'capacity' => 22,
            'status' => 'Active',
        ]);

        $this->assertDatabaseHas('fields', [
            'name' => 'Veld Multi',
            'type' => 'Multi-Purpose',
            'hourly_rate' => 20.00,
            'night_hourly_rate' => 30.00,
            'night_time_start' => '18:30',
            'capacity' => 30,
            'status' => 'Active',
        ]);

        $this->assertDatabaseHas('fields', [
            'name' => 'Veld Bolas',
            'type' => 'Multi-Purpose',
            'hourly_rate' => 0.00,
            'night_hourly_rate' => 15.00,
            'night_time_start' => '18:30',
            'capacity' => 16,
            'status' => 'Active',
        ]);

        $this->assertDatabaseHas('fields', [
            'name' => 'Patio Area',
            'type' => 'Multi-Purpose',
            'hourly_rate' => 35.00,
            'night_hourly_rate' => 35.00,
            'night_time_start' => '18:30',
            'capacity' => 50,
            'status' => 'Active',
        ]);

        // Assert amenities were attached correctly
        $soccerField = Field::where('name', 'Veld futbol')->first();
        $this->assertNotNull($soccerField);

        $amenityNames = $soccerField->amenities->pluck('name')->toArray();
        $this->assertContains('Lighting', $amenityNames);
        $this->assertContains('Parking', $amenityNames);
        $this->assertContains('Restrooms', $amenityNames);
        $this->assertContains('Equipment Available', $amenityNames);
        $this->assertContains('Scoreboard', $amenityNames);
        $this->assertContains('Spectator Seating', $amenityNames);

        // Assert patio area has correct amenities
        $patioArea = Field::where('name', 'Patio Area')->first();
        $patioAmenities = $patioArea->amenities->pluck('name')->toArray();
        $this->assertContains('Parking', $patioAmenities);
        $this->assertContains('Restrooms', $patioAmenities);
        $this->assertContains('Spectator Seating', $patioAmenities);
        $this->assertContains('Sound System', $patioAmenities);
        $this->assertContains('WiFi', $patioAmenities);

        // Assert all FPMP fields have required attributes
        $fpmpFields = Field::whereIn('name', ['Veld futbol', 'Veld Multi', 'Veld Bolas', 'Patio Area'])->get();
        foreach ($fpmpFields as $field) {
            $this->assertNotNull($field->opening_time);
            $this->assertNotNull($field->closing_time);
            $this->assertNotNull($field->min_booking_hours);
            $this->assertNotNull($field->max_booking_hours);
            $this->assertNotNull($field->night_hourly_rate);
            $this->assertNotNull($field->night_time_start);
            $this->assertNotNull($field->description);
            $this->assertEquals('08:00', $field->opening_time);
            $this->assertEquals('22:00', $field->closing_time);
            $this->assertEquals('18:30', $field->night_time_start);
        }

        // Assert specific descriptions are set correctly
        $soccerField = Field::where('name', 'Veld futbol')->first();
        $this->assertStringContainsString('Professional soccer field', $soccerField->description);
        $this->assertStringContainsString('FPMP Sport Park Mariepampoen', $soccerField->description);

        $patioArea = Field::where('name', 'Patio Area')->first();
        $this->assertStringContainsString('Covered patio area', $patioArea->description);
        $this->assertStringContainsString('events, parties, and social gatherings', $patioArea->description);
    }

    #[Test]
    public function fpmp_field_seeder_prevents_duplicate_fields()
    {
        // First seed amenities
        $this->seed(AmenitySeeder::class);

        // Seed FPMP fields twice
        $this->seed(FPMPFieldSeeder::class);
        $this->seed(FPMPFieldSeeder::class);

        // Assert only 4 fields were created (no duplicates)
        $this->assertDatabaseCount('fields', 4);

        // Assert each FPMP field exists only once
        $this->assertEquals(1, Field::where('name', 'Veld futbol')->count());
        $this->assertEquals(1, Field::where('name', 'Veld Multi')->count());
        $this->assertEquals(1, Field::where('name', 'Veld Bolas')->count());
        $this->assertEquals(1, Field::where('name', 'Patio Area')->count());
    }

    #[Test]
    public function fpmp_field_seeder_handles_missing_amenities_gracefully()
    {
        // Seed only some amenities
        Amenity::create(['name' => 'Lighting', 'is_active' => true]);
        Amenity::create(['name' => 'Parking', 'is_active' => true]);
        Amenity::create(['name' => 'Restrooms', 'is_active' => true]);

        // Seed FPMP fields (should work even if some amenities don't exist)
        $this->seed(FPMPFieldSeeder::class);

        // Assert fields were still created
        $this->assertDatabaseCount('fields', 4);

        // Assert available amenities were attached
        $soccerField = Field::where('name', 'Veld futbol')->first();
        $amenityNames = $soccerField->amenities->pluck('name')->toArray();
        $this->assertContains('Lighting', $amenityNames);
        $this->assertContains('Parking', $amenityNames);
        $this->assertContains('Restrooms', $amenityNames);

        // Non-existent amenities should not be attached
        $this->assertNotContains('Equipment Available', $amenityNames);
        $this->assertNotContains('Scoreboard', $amenityNames);
        $this->assertNotContains('Spectator Seating', $amenityNames);
    }

    #[Test]
    public function fpmp_field_seeder_creates_fields_with_correct_booking_constraints()
    {
        // First seed amenities
        $this->seed(AmenitySeeder::class);

        // Seed FPMP fields
        $this->seed(FPMPFieldSeeder::class);

        // Check specific booking constraints for different field types
        $soccerField = Field::where('name', 'Veld futbol')->first();
        $this->assertEquals('1.0', $soccerField->min_booking_hours); // Decimal cast returns string
        $this->assertEquals('10.0', $soccerField->max_booking_hours);

        $bolasField = Field::where('name', 'Veld Bolas')->first();
        $this->assertEquals('1.0', $bolasField->min_booking_hours);
        $this->assertEquals('10.0', $bolasField->max_booking_hours);

        $patioArea = Field::where('name', 'Patio Area')->first();
        $this->assertEquals('2.0', $patioArea->min_booking_hours); // Patio Area has different min hours
        $this->assertEquals('10.0', $patioArea->max_booking_hours);

        // Verify that all fields have the same max booking hours as defined in seeder
        $allFields = Field::whereIn('name', ['Veld futbol', 'Veld Multi', 'Veld Bolas', 'Patio Area'])->get();
        foreach ($allFields as $field) {
            $this->assertEquals('10.0', $field->max_booking_hours);
        }
    }
}
