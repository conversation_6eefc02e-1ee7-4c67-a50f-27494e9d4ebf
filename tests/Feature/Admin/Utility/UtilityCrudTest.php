<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UtilityCrudTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected $member;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->member = User::factory()->create(['role' => 'member']);
    }

    #[Test]
    public function admin_can_view_utilities_index()
    {
        // Create some test utilities
        Utility::factory()->count(3)->create();

        $response = $this->actingAs($this->admin)
            ->get('/admin/utilities');

        $response->assertStatus(200);
        $response->assertSee('Utilities Management');
        $response->assertSee('Add New Utility');
    }

    #[Test]
    public function member_cannot_access_utilities_index()
    {
        $response = $this->actingAs($this->member)
            ->get('/admin/utilities');

        $response->assertStatus(403);
    }

    #[Test]
    public function guest_cannot_access_utilities_index()
    {
        $response = $this->get('/admin/utilities');

        $response->assertRedirect('/login');
    }

    #[Test]
    public function admin_can_view_create_utility_form()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/utilities/create');

        $response->assertStatus(200);
        $response->assertSee('Create New Utility');
        $response->assertSee('name="name"', false);
        $response->assertSee('name="description"', false);
        $response->assertSee('name="hourly_rate"', false);
        // Active status field should not be present in create form
    }

    #[Test]
    public function admin_can_create_utility_with_valid_data()
    {
        $utilityData = [
            'name' => 'Test Utility',
            'description' => 'This is a test utility',
            'hourly_rate' => 25.00,
            'icon_class' => 'ri-check-line',
            // is_active field removed from form - defaults to true
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/utilities', $utilityData);

        $response->assertRedirect();
        $this->assertDatabaseHas('utilities', [
            'name' => 'Test Utility',
            'description' => 'This is a test utility',
            'hourly_rate' => 25.00,
            'icon_class' => 'ri-check-line',
            'is_active' => true,
        ]);
    }

    #[Test]
    public function utility_creation_requires_name()
    {
        $utilityData = [
            'description' => 'This is a test utility',
            'hourly_rate' => 25.00,
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/utilities', $utilityData);

        $response->assertSessionHasErrors('name');
        $this->assertDatabaseMissing('utilities', [
            'description' => 'This is a test utility',
        ]);
    }

    #[Test]
    public function utility_name_must_be_unique()
    {
        Utility::factory()->create(['name' => 'Existing Utility']);

        $utilityData = [
            'name' => 'Existing Utility',
            'description' => 'Test description',
            'hourly_rate' => 25.00,
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/utilities', $utilityData);

        $response->assertSessionHasErrors('name');
    }

    #[Test]
    public function admin_can_view_utility_details()
    {
        $utility = Utility::factory()->create();

        $response = $this->actingAs($this->admin)
            ->get("/admin/utilities/{$utility->id}");

        $response->assertStatus(200);
        $response->assertSee($utility->name);
        $response->assertSee($utility->description);
    }

    #[Test]
    public function admin_can_view_edit_utility_form()
    {
        $utility = Utility::factory()->create();

        $response = $this->actingAs($this->admin)
            ->get("/admin/utilities/{$utility->id}/edit");

        $response->assertStatus(200);
        $response->assertSee('Edit Utility');
        $response->assertSee($utility->name);
        $response->assertSee($utility->icon_class);
    }

    #[Test]
    public function admin_can_update_utility()
    {
        $utility = Utility::factory()->create([
            'name' => 'Original Name',
            'description' => 'Original description',
            'hourly_rate' => 25.00,
            'icon_class' => 'ri-star-line',
            'is_active' => true,
        ]);

        $updateData = [
            'name' => 'Updated Name',
            'description' => 'Updated description',
            'hourly_rate' => 35.00,
            'icon_class' => 'ri-check-line',
            // is_active not included to simulate unchecked checkbox
        ];

        $response = $this->actingAs($this->admin)
            ->put("/admin/utilities/{$utility->id}", $updateData);

        $response->assertRedirect('/admin/utilities');

        $utility->refresh();
        $this->assertEquals('Updated Name', $utility->name);
        $this->assertEquals('Updated description', $utility->description);
        $this->assertEquals(35.00, $utility->hourly_rate);
        $this->assertEquals('ri-check-line', $utility->icon_class);
        $this->assertFalse($utility->is_active);
    }

    #[Test]
    public function admin_can_update_utility_active_status_to_checked()
    {
        $utility = Utility::factory()->create([
            'name' => 'Test Utility',
            'description' => 'Test description',
            'hourly_rate' => 25.00,
            'icon_class' => 'ri-star-line',
            'is_active' => false,
        ]);

        $updateData = [
            'name' => 'Test Utility',
            'description' => 'Test description',
            'hourly_rate' => 25.00,
            'icon_class' => 'ri-star-line',
            'is_active' => '1', // Checkbox checked
        ];

        $response = $this->actingAs($this->admin)
            ->put("/admin/utilities/{$utility->id}", $updateData);

        $response->assertRedirect('/admin/utilities');

        $utility->refresh();
        $this->assertTrue($utility->is_active);
    }

    #[Test]
    public function admin_can_update_utility_active_status_to_unchecked()
    {
        $utility = Utility::factory()->create([
            'name' => 'Test Utility',
            'description' => 'Test description',
            'hourly_rate' => 25.00,
            'icon_class' => 'ri-star-line',
            'is_active' => true,
        ]);

        $updateData = [
            'name' => 'Test Utility',
            'description' => 'Test description',
            'hourly_rate' => 25.00,
            'icon_class' => 'ri-star-line',
            'is_active' => '0', // Hidden input value when checkbox unchecked
        ];

        $response = $this->actingAs($this->admin)
            ->put("/admin/utilities/{$utility->id}", $updateData);

        $response->assertRedirect('/admin/utilities');

        $utility->refresh();
        $this->assertFalse($utility->is_active);
    }

    #[Test]
    public function admin_can_delete_utility_not_in_use()
    {
        $utility = Utility::factory()->create();

        $response = $this->actingAs($this->admin)
            ->delete("/admin/utilities/{$utility->id}");

        $response->assertRedirect('/admin/utilities');
        $this->assertSoftDeleted('utilities', ['id' => $utility->id]);
    }

    #[Test]
    public function admin_cannot_delete_utility_in_use_by_fields()
    {
        $utility = Utility::factory()->create();
        $field = Field::factory()->create();
        $field->utilities()->attach($utility);

        $response = $this->actingAs($this->admin)
            ->delete("/admin/utilities/{$utility->id}");

        $response->assertRedirect('/admin/utilities');
        $response->assertSessionHas('error');
        $this->assertDatabaseHas('utilities', ['id' => $utility->id]);
    }

    #[Test]
    public function admin_can_perform_bulk_activate_action()
    {
        $utilities = Utility::factory()->count(3)->create(['is_active' => false]);
        $utilityIds = $utilities->pluck('id')->toArray();

        $response = $this->actingAs($this->admin)
            ->post('/admin/utilities/bulk-action', [
                'action' => 'activate',
                'utilities' => $utilityIds,
            ]);

        $response->assertRedirect('/admin/utilities');

        foreach ($utilities as $utility) {
            $utility->refresh();
            $this->assertTrue($utility->is_active);
        }
    }

    #[Test]
    public function admin_can_perform_bulk_deactivate_action()
    {
        $utilities = Utility::factory()->count(3)->create(['is_active' => true]);
        $utilityIds = $utilities->pluck('id')->toArray();

        $response = $this->actingAs($this->admin)
            ->post('/admin/utilities/bulk-action', [
                'action' => 'deactivate',
                'utilities' => $utilityIds,
            ]);

        $response->assertRedirect('/admin/utilities');

        foreach ($utilities as $utility) {
            $utility->refresh();
            $this->assertFalse($utility->is_active);
        }
    }

    #[Test]
    public function admin_can_perform_bulk_delete_action_on_unused_utilities()
    {
        $utilities = Utility::factory()->count(3)->create();
        $utilityIds = $utilities->pluck('id')->toArray();

        $response = $this->actingAs($this->admin)
            ->post('/admin/utilities/bulk-action', [
                'action' => 'delete',
                'utilities' => $utilityIds,
            ]);

        $response->assertRedirect('/admin/utilities');

        foreach ($utilities as $utility) {
            $this->assertSoftDeleted('utilities', ['id' => $utility->id]);
        }
    }

    #[Test]
    public function admin_cannot_bulk_delete_utilities_in_use()
    {
        $utilities = Utility::factory()->count(2)->create();
        $field = Field::factory()->create();
        $field->utilities()->attach($utilities->first());

        $utilityIds = $utilities->pluck('id')->toArray();

        $response = $this->actingAs($this->admin)
            ->post('/admin/utilities/bulk-action', [
                'action' => 'delete',
                'utilities' => $utilityIds,
            ]);

        $response->assertRedirect('/admin/utilities');
        $response->assertSessionHas('error');

        foreach ($utilities as $utility) {
            $this->assertDatabaseHas('utilities', ['id' => $utility->id]);
        }
    }

    #[Test]
    public function utility_defaults_to_active_when_created()
    {
        // Test that new utilities default to active status
        $utilityData = [
            'name' => 'New Utility',
            'description' => 'Test description',
            'hourly_rate' => 25.00,
            'icon_class' => 'ri-settings-line',
            // is_active field removed from form - should default to true
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/utilities', $utilityData);

        $response->assertRedirect();
        $this->assertDatabaseHas('utilities', [
            'name' => 'New Utility',
            'icon_class' => 'ri-settings-line',
            'is_active' => true, // Should default to active
        ]);
    }

    #[Test]
    public function field_utility_relationship_works()
    {
        $utility = Utility::factory()->create();
        $field = Field::factory()->create();

        // Test attaching utility to field
        $field->utilities()->attach($utility);

        $this->assertTrue($field->utilities->contains($utility));
        $this->assertTrue($utility->fields->contains($field));
        $this->assertEquals(1, $utility->fields_count);
    }

    #[Test]
    public function utility_scopes_work_correctly()
    {
        Utility::factory()->create(['is_active' => true]);
        Utility::factory()->create(['is_active' => false]);

        $activeUtilities = Utility::active()->get();
        $inactiveUtilities = Utility::inactive()->get();

        $this->assertEquals(1, $activeUtilities->count());
        $this->assertEquals(1, $inactiveUtilities->count());
    }

    #[Test]
    public function utility_model_methods_work_correctly()
    {
        $utility = Utility::factory()->create(['is_active' => true]);

        // Test status methods
        $this->assertEquals('Active', $utility->status_text);
        $this->assertEquals('bg-success', $utility->status_badge_class);
        $this->assertTrue($utility->canBeDeleted());

        // Test toggle methods
        $utility->deactivate();
        $this->assertFalse($utility->is_active);

        $utility->activate();
        $this->assertTrue($utility->is_active);

        $utility->toggleStatus();
        $this->assertFalse($utility->is_active);
    }
}
